'use client';

import { Smartphone, Download } from 'lucide-react';

interface AppStoreBadgesProps {
  variant?: 'default' | 'large';
  className?: string;
}

export default function AppStoreBadges({ variant = 'default', className = '' }: AppStoreBadgesProps) {
  const isLarge = variant === 'large';

  return (
    <div className={`flex flex-col sm:flex-row gap-4 ${className}`}>
      {/* Google Play Store Badge */}
      <a
        href="#"
        className={`group flex items-center gap-3 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white ${
          isLarge ? 'px-6 py-4' : 'px-4 py-3'
        } rounded-xl transition-all duration-300 shadow-lg hover:shadow-pink-500/25 hover:scale-105 min-w-[200px]`}
      >
        <div className={`${isLarge ? 'p-2' : 'p-1'} bg-white/20 rounded-lg`}>
          <Smartphone size={isLarge ? 24 : 20} />
        </div>
        <div className="text-left">
          <div className={`${isLarge ? 'text-xs' : 'text-[10px]'} text-white/80 uppercase tracking-wide`}>
            Get it on
          </div>
          <div className={`${isLarge ? 'text-lg' : 'text-sm'} font-semibold leading-tight`}>
            Google Play
          </div>
        </div>
      </a>

      {/* Apple App Store Badge */}
      <a
        href="#"
        className={`group flex items-center gap-3 bg-black hover:bg-gray-900 text-white ${
          isLarge ? 'px-6 py-4' : 'px-4 py-3'
        } rounded-xl transition-all duration-300 shadow-lg hover:shadow-gray-500/25 hover:scale-105 border border-white/20 hover:border-white/30 min-w-[200px]`}
      >
        <div className={`${isLarge ? 'p-2' : 'p-1'} bg-white/10 rounded-lg`}>
          <Download size={isLarge ? 24 : 20} />
        </div>
        <div className="text-left">
          <div className={`${isLarge ? 'text-xs' : 'text-[10px]'} text-white/80 uppercase tracking-wide`}>
            Download on the
          </div>
          <div className={`${isLarge ? 'text-lg' : 'text-sm'} font-semibold leading-tight`}>
            App Store
          </div>
        </div>
      </a>
    </div>
  );
}

// Alternative compact version for navigation
export function CompactAppStoreBadges({ className = '' }: { className?: string }) {
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Google Play Store */}
      <a
        href="https://play.google.com/store/apps/details?id=urvashi.indian.ai.girlfriend.gf.virtual.dating.lover"
        className="flex items-center gap-2 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white px-3 py-2 rounded-full transition-all duration-300 shadow-lg hover:shadow-pink-500/25 hover:scale-105"
      >
        <Smartphone size={16}  />
        <span className="text-xs font-medium hidden sm:inline">Play Store</span>
      </a>

      {/* Apple App Store */}
      <a
        href="https://apps.apple.com/us/app/urvashi-indian-ai-girlfriend/id6745728476"
        className="flex items-center gap-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white px-3 py-2 rounded-full transition-all duration-300 border border-white/20 hover:border-white/30 hover:scale-105"
      >
        <Download size={16} />
        <span className="text-xs font-medium hidden sm:inline">App Store</span>
      </a>
    </div>
  );
}
